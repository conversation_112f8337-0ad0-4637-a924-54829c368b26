#!/bin/bash

echo "Testing the loop structure..."

STATUSES=(live test quit)

while true; do
  echo ""
  echo "=== Loop iteration ==="
  
  PS3="Select status: "
  select STATUS in "${STATUSES[@]}"; do
    if [[ -n "$STATUS" ]]; then
      echo "Selected: $STATUS"
      break
    else
      echo "Invalid selection."
    fi
  done
  
  if [[ "$STATUS" == "quit" ]]; then
    echo "Quitting..."
    break
  fi
  
  read -p "Enter org [3]: " ORG
  ORG=${ORG:-3}
  echo "Org: $ORG"
  
  # Simulate finding results
  if [[ "$STATUS" == "test" ]]; then
    echo "No results found. Try again."
    continue
  fi
  
  # Simulate shipment selection
  echo "Found shipments. Select one:"
  select SHIPMENT in "Shipment 1" "Shipment 2" "Back"; do
    if [[ -n "$SHIPMENT" ]]; then
      if [[ "$SHIPMENT" == "Back" ]]; then
        echo "Going back..."
        break  # Break from select, continue while loop
      else
        echo "Selected: $SHIPMENT"
        break 2  # Break from both select and while
      fi
    else
      echo "Invalid selection."
    fi
  done
done

echo "Done!" 