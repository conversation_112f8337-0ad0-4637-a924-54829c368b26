#!/bin/bash

echo "Testing Node.js call within loop..."

while true; do
  echo ""
  echo "=== Testing Node.js call ==="
  
  read -p "Enter status to test [live]: " STATUS
  STATUS=${STATUS:-live}
  
  read -p "Enter org [3]: " ORG  
  ORG=${ORG:-3}
  
  echo "About to call Node.js script..."
  echo "Command: node src/core-agent/testing/find-test-shipments-menu.js --status=\"$STATUS\" --org=\"$ORG\" --limit=10"
  
  # Test the actual call that's failing
  RESULT=""
  if RESULT=$(node src/core-agent/testing/find-test-shipments-menu.js --status="$STATUS" --org="$ORG" --limit=10 2>&1); then
    echo "Node.js call succeeded"
    if [[ -n "$RESULT" ]]; then
      echo "Got results:"
      echo "$RESULT" | head -3
    else
      echo "No results returned"
    fi
  else
    echo "Node.js call failed with:"
    echo "$RESULT"
  fi
  
  read -p "Continue testing? (y/n): " CONTINUE
  if [[ "$CONTINUE" != "y" ]]; then
    break
  fi
done

echo "Test completed" 