# Test Shipments Report

Generated: 2025-07-09  
Database Query Date: 2025-07-09 19:00 UTC  
Purpose: Comprehensive test data for core-agent testing and validation

## Overview

This report contains test shipments from organizations 1 and 3 with diverse customs statuses, validation scenarios, and edge cases for comprehensive testing of the core-agent system.

## Organization 3 (Demo Organization - Recommended for Testing)

### Available Customs Statuses
- `pending-commercial-invoice` (44 shipments)
- `live` (20 shipments)
- `pending-confirmation` (11 shipments)

### Test Shipments - Organization 3

| ID | HBL Number | Customs Status | Transaction Number | MBL Number | Cargo Control Number | Port Code | Validation Issues |
|---|---|---|---|---|---|---|---|
| **887** | `TEST-49085727-ZUVL` | pending-commercial-invoice | null | **null** | ABC123456789 | 0123 | Missing MBL Number |
| **886** | `TEST-48721590-2ZP4` | pending-commercial-invoice | null | **null** | ABCDABC-487215902ZP4 | 0315 | Missing MBL Number |
| **885** | `TEST-47702196-06AQ` | pending-commercial-invoice | null | **null** | ABCDABC-47702196O6AQ | 0315 | Missing MBL Number |
| **884** | `TEST-43234669-8TOZ` | pending-commercial-invoice | null | **null** | ABCDABC-432346698TOZ | 0315 | Missing MBL Number |
| **883** | `TEST-34300069-XZCW` | pending-commercial-invoice | null | **null** | ABCDABC-34300069XZCW | 0315 | Missing MBL Number |
| **842** | `BL-TEST-1750630331751-E6LLV0` | pending-confirmation | null | **null** | - | - | Missing MBL Number |
| **840** | `BL-TEST-1750588491476-V51DXW` | pending-confirmation | null | **null** | - | - | Missing MBL Number |
| **839** | `BL-TEST-1750580192863-55DVJW` | pending-confirmation | null | **null** | - | - | Missing MBL Number |
| **837** | `BL-TEST-1750564778565-EPE2Q7` | pending-confirmation | null | **null** | - | - | Missing MBL Number |
| **836** | `BL-TEST-1750563378134-1JIX3C` | pending-confirmation | null | **null** | - | - | Missing MBL Number |
| **879** | `TEST-99349853-A4K6` | live | 10005202196976 | **null** | **empty string** | 0315 | Missing MBL Number, Empty CCN |
| **878** | `TEST-77079412-2K36` | live | 10005202196965 | **null** | ABCDABC-770794122K36 | 0315 | Missing MBL Number |
| **877** | `TEST-74676540-XL97` | live | 10005202196954 | **null** | ABCDABC-74676540XL97 | 0315 | Missing MBL Number |
| **876** | `TEST-73661255-A7D2` | live | 10005202196943 | **null** | ABCDABC-73661255A7D2 | 0315 | Missing MBL Number |
| **875** | `TEST-72451928-371V` | live | 10005202196910 | **null** | ABCDABC-72451928371V | 0315 | Missing MBL Number |

## Organization 1 (Additional Test Cases)

### Available Customs Statuses
- `pending-commercial-invoice` (110 shipments)
- `live` (30 shipments)
- `pending-confirmation` (27 shipments)
- `released` (5 shipments) ⭐
- `entry-submitted` (3 shipments) ⭐

### Test Shipments - Organization 1

| ID | HBL Number | Customs Status | Transaction Number | MBL Number | Cargo Control Number | Port Code | Validation Issues |
|---|---|---|---|---|---|---|---|
| **888** | `TEST-49790050-I8QJ` | pending-commercial-invoice | null | **null** | **null** | **null** | Multiple Missing Fields |
| **766** | `NBBH24100003` | pending-commercial-invoice | null | **null** | 8FPDNBBH24120005 | 0495 | Missing MBL Number |
| **765** | `NBBH24100003` | pending-commercial-invoice | null | **null** | **null** | **null** | Multiple Missing Fields |
| **724** | `ZTMQQTN401890` | pending-confirmation | null | **null** | **null** | **null** | Multiple Missing Fields |
| **391** | `16H5PARS231912` | pending-confirmation | null | **null** | 16H5PARS231912 | **null** | Missing MBL Number, Port Code |
| **333** | `OWSH24100346` | pending-confirmation | null | **null** | 8FN0OWSH24100346 | 0395 | Missing MBL Number |
| **453** | `JIRA-TEST-1747746505495` | live | TN-JIRA-12345 | **null** | **null** | **null** | Multiple Missing Fields |
| **452** | `JIRA-TEST-1747746365896` | live | TN-JIRA-12345 | **null** | **null** | **null** | Multiple Missing Fields |
| **451** | `JIRA-TEST-1747746087106` | live | TN-JIRA-12345 | **null** | **null** | **null** | Multiple Missing Fields |
| **258** | `P308583` | **entry-submitted** ⭐ | 10005202165489 | **null** | 20WQPARS126838 | 0453 | Missing MBL Number |
| **248** | `CSH24120013` | **entry-submitted** ⭐ | 10005202165183 | **null** | 8F5LCSH24120013 | 0809 | Missing MBL Number |
| **247** | `014-23168423` | **entry-submitted** ⭐ | 10005202165172 | **null** | 806USAI8829 | **null** | Missing MBL Number, Port Code |
| **336** | `SZ24100122` | **released** ⭐ | 10005202167129 | **null** | 8887SZ24100122 | 0702 | Missing MBL Number |
| **331** | `GZFWL2412356` | **released** ⭐ | 10005202167210 | **null** | 8054GLGL24120461 | 0497 | Missing MBL Number |

## Common Validation Issues Found

### 1. Missing MBL Numbers
- **All shipments** across both organizations have `null` MBL numbers
- This is a consistent validation issue for testing business rule evaluation

### 2. Missing Required Fields (Org 1 Only)
- **Multiple null fields**: Some shipments have null values for cargo control numbers, port codes
- **ID 888**: Missing cargo control number AND port code - most severe case

### 3. Empty String vs Null
- **ID 879** (Org 3): Has empty string for cargo control number instead of null
- Good test case for string validation patterns

### 4. Advanced Customs Statuses (Org 1 Only)
- **Released shipments**: Final stage of customs clearance
- **Entry-submitted shipments**: Intermediate stage with transaction numbers

## Testing Recommendations

### For Core-Agent Testing Use:
1. **Organization 3 (Demo)**: Primary testing environment as specified in CLAUDE.md
2. **Organization 1**: Additional test cases for advanced statuses and edge cases

### Test Scenarios by Status:
- **pending-commercial-invoice**: Test document requirement validation
- **pending-confirmation**: Test confirmation workflow triggers
- **live**: Test transaction number handling and status updates
- **released**: Test final stage processing (Org 1 only)
- **entry-submitted**: Test intermediate stage handling (Org 1 only)

### Validation Testing:
- Use shipments with missing MBL numbers for business rule testing
- Test empty string vs null handling with ID 879
- Test multiple missing fields with ID 888, 765, 724

### Quick Test Commands:
```bash
# Demo org testing (preferred)
cd apps/portal-api
./src/core-agent/testing/run-processor-test-with-logs.sh --intent=GET_SHIPMENT_STATUS --shipment-id=887

# Database queries
node src/core-agent/testing/db-query.js sql "SELECT * FROM shipment WHERE id = 887"

# E2E testing
./src/core-agent/testing/run-e2e-with-logs.sh
```

## Notes

- **No `requiresReupload` flags**: No shipments currently marked as requiring reupload
- **All shipments have documents**: No shipments found without associated documents
- **Consistent patterns**: Missing MBL numbers appear to be a systematic issue across all test data
- **Real validation issues**: These are actual data inconsistencies, not artificially created test cases

---

*This report provides a comprehensive view of available test shipments for core-agent development and validation testing.*