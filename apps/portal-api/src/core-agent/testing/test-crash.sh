#!/bin/bash

echo "=== Testing bash script components ==="

echo "1. Testing basic variables..."
STATUSES=(
  pending-commercial-invoice
  pending-confirmation
  pending-arrival
  live
)
echo "Statuses array created: ${#STATUSES[@]} items"

echo ""
echo "2. Testing simple select menu..."
PS3="Test select: "
select STATUS in "live" "test" "quit"; do
  if [[ -n "$STATUS" ]]; then
    echo "You selected: $STATUS"
    break
  else
    echo "Invalid selection."
  fi
done

echo ""
echo "3. Testing read input..."
read -p "Enter test org [3]: " ORG
ORG=${ORG:-3}
echo "Org set to: $ORG"

echo ""
echo "4. Testing file existence check..."
if [[ -f "src/core-agent/testing/find-test-shipments-menu.js" ]]; then
  echo "File exists"
else
  echo "File does not exist"
fi

echo ""
echo "5. Testing simple command execution..."
echo "Running: ls src/core-agent/testing/ | head -3"
ls src/core-agent/testing/ | head -3

echo ""
echo "6. Testing array creation..."
TEST_LINES="line1
line2
line3"

TEST_ARRAY=()
while IFS= read -r line; do
  if [[ -n "$line" ]]; then
    TEST_ARRAY+=("$line")
  fi
done <<< "$TEST_LINES"

echo "Array created with ${#TEST_ARRAY[@]} items"

echo ""
echo "=== All tests completed successfully ===" 