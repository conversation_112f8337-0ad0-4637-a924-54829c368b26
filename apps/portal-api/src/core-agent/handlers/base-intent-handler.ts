import { Logger } from "@nestjs/common";
import { Inten<PERSON><PERSON><PERSON><PERSON>, IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ShipmentContext, ShipmentContextWithServices } from "../../agent-context";
import {
  ResponseFragment,
  ValidatedIntent,
  FragmentContext,
  ConsolidatedFragmentContext
} from "../types/response-fragment.types";

/**
 * Abstract base class for all intent handlers providing common functionality.
 * Intent handlers should extend this class to inherit useful helper methods.
 */
export abstract class BaseIntentHandler implements IntentHandler {
  protected readonly logger = new Logger(this.constructor.name);

  /**
   * Classification metadata for dynamic LLM prompt generation.
   * Each concrete handler must define this with their specific intent, description, and examples.
   */
  abstract readonly classificationMeta: IntentClassificationMeta;

  /**
   * Handle the validated intent and return response fragments.
   * Each concrete handler must implement this method.
   */
  abstract handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]>;

  // ===== HELPER METHODS =====

  /**
   * Add compliance-related fragments if the shipment has compliance issues.
   * This is commonly needed across multiple intent handlers.
   */
  protected addComplianceFragmentsIfNeeded(
    fragments: ResponseFragment[],
    context: ShipmentContext,
    basePriority: number = 10
  ): void {
    if (context.missingDocuments.length > 0) {
      fragments.push({
        template: "acknowledgement-missing-docs",
        priority: basePriority
      });
    }

    if (context.complianceErrors.length > 0) {
      fragments.push({
        template: "compliance-errors",
        priority: basePriority + 1
      });
    }

    if (context.nonCompliantInvoices.length > 0) {
      fragments.push({
        template: "compliance-errors",
        priority: basePriority + 2
      });
    }
  }

  /**
   * Add a submission required notice if the shipment isn't submitted yet.
   * Commonly used when features require customs submission.
   */
  protected addSubmissionRequiredNoticeIfNeeded(
    fragments: ResponseFragment[],
    context: ShipmentContext,
    priority: number = 20
  ): void {
    if (!context.isSubmitted) {
      fragments.push({
        template: "submission-required-notice",
        priority
      });
    }
  }

  /**
   * Send a backoffice alert email for manual processing requests.
   * Returns fragment context with alert status for merging.
   */
  protected async sendBackofficeAlert(
    alertType: string,
    shipmentId: number,
    userInstructions: string[],
    emailService: any,
    organizationId: number
  ): Promise<Partial<FragmentContext>> {
    try {
      // Check if email service is available
      if (!emailService) {
        this.logger.warn(`Email service not available for ${alertType} alert on shipment ${shipmentId}`);
        return { backofficeAlerts: {} };
      }

      this.logger.log(`[BACKOFFICE_ALERT] Requesting ${alertType} alert on shipment ${shipmentId}`);

      const subject = `${alertType} - Shipment ID: ${shipmentId}`;
      const body = [
        `${alertType} requested for shipment ID: ${shipmentId}.`,
        "",
        "User Instructions:",
        ...userInstructions.map((instruction) => `- ${instruction}`),
        "",
        "Please review and take appropriate action."
      ].join("\n");

      await emailService.sendBackofficeAlert(subject, body, organizationId);

      this.logger.log(`[BACKOFFICE_ALERT] Sent ${alertType} alert for shipment ${shipmentId}`);

      // Return appropriate alert status based on alert type
      const alertKey = this.getAlertKeyFromType(alertType);
      return {
        backofficeAlerts: {
          [alertKey]: true
        }
      };
    } catch (error) {
      this.logger.error(
        `Failed to send ${alertType} alert for shipment ${shipmentId}: ${error.message}`,
        error.stack
      );
      return { backofficeAlerts: {} };
    }
  }

  /**
   * Map alert type to the appropriate backoffice alert key
   */
  private getAlertKeyFromType(alertType: string): string {
    if (alertType.toLowerCase().includes("rush")) {
      return "rushProcessingSent";
    }
    if (alertType.toLowerCase().includes("hold") || alertType.toLowerCase().includes("cancel")) {
      return "holdShipmentSent";
    }
    if (alertType.toLowerCase().includes("manual")) {
      return "manualProcessingSent";
    }
    return "manualProcessingSent"; // Default fallback
  }

  /**
   * Safe wrapper for executing potentially failing operations.
   * Returns success/failure and logs errors automatically.
   */
  protected async safeExecute<T>(
    operation: () => Promise<T>,
    errorContext: string
  ): Promise<{ success: boolean; result?: T; error?: Error }> {
    try {
      const result = await operation();
      return { success: true, result };
    } catch (error) {
      this.logger.error(`${errorContext}: ${error.message}`, error.stack);
      return { success: false, error };
    }
  }

  /**
   * Extract instructions from a validated intent.
   * Uses the existing ValidatedEmailAction.instructions field.
   */
  protected extractInstructions(validatedIntent: ValidatedIntent): string[] {
    return validatedIntent.instructions || [];
  }

  /**
   * Create a high-priority fragment that should appear near the top of the response.
   */
  protected createHighPriorityFragment(
    template: string,
    fragmentContext?: ResponseFragment["fragmentContext"]
  ): ResponseFragment {
    return {
      template,
      priority: 1,
      fragmentContext
    };
  }

  /**
   * Create a medium-priority fragment for standard responses.
   */
  protected createMediumPriorityFragment(
    template: string,
    fragmentContext?: ResponseFragment["fragmentContext"]
  ): ResponseFragment {
    return {
      template,
      priority: 10,
      fragmentContext
    };
  }

  /**
   * Create a low-priority fragment for supplementary information.
   */
  protected createLowPriorityFragment(
    template: string,
    fragmentContext?: ResponseFragment["fragmentContext"]
  ): ResponseFragment {
    return {
      template,
      priority: 20,
      fragmentContext
    };
  }

  // ===== CONSOLIDATED TEMPLATE SYSTEM METHODS =====

  /**
   * Create standardized fragments using consolidated templates.
   * This is the main method for generating consistent response structures.
   */
  protected createConsolidatedFragments(
    mainMessages: Array<{ content?: string; type?: string; priority: number; attachments?: any }>,
    context: ShipmentContext,
    options: {
      showValidationIssues?: boolean;
      showDocumentStatus?: boolean;
      showAdditionalValidation?: boolean;
      validationIssues?: ConsolidatedFragmentContext["validationIssues"];
      additionalValidation?: { issues: string[] };
    } = {}
  ): ResponseFragment[] {
    // Build validation issues if not provided
    const validationIssues = options.validationIssues ?? this.buildValidationIssues(context);

    // Extract side effects from mainMessages for processor collection
    const sideEffects: any = {};

    // Look for CAD document in mainMessages attachments
    const cadMessage = mainMessages.find((msg) => msg.attachments?.cadDocument);
    if (cadMessage?.attachments?.cadDocument) {
      sideEffects.cadDocument = cadMessage.attachments.cadDocument;
      this.logger.log(
        `[PDF_ATTACHMENT_TRACKING] Found CAD document in mainMessages: ${JSON.stringify({
          fileName: cadMessage.attachments.cadDocument.fileName,
          mimeType: cadMessage.attachments.cadDocument.mimeType,
          b64DataLength: cadMessage.attachments.cadDocument.b64Data?.length || 0
        })}`
      );
    } else {
      this.logger.log(`[PDF_ATTACHMENT_TRACKING] No CAD document found in mainMessages`);
    }

    // Look for RNS data in mainMessages attachments
    const rnsMessage = mainMessages.find((msg) => msg.attachments?.rnsData);
    if (rnsMessage?.attachments?.rnsData) {
      sideEffects.rnsProofData = rnsMessage.attachments.rnsData;
    }

    const consolidatedContext = {
      ...context,
      mainMessages,
      validationIssues,
      additionalValidation: options.additionalValidation,
      showValidationIssues:
        options.showValidationIssues ??
        (!!validationIssues &&
          (validationIssues.missingDocuments?.length > 0 || validationIssues.missingFields?.length > 0)),
      showDocumentStatus:
        options.showDocumentStatus ??
        ["pending-commercial-invoice", "pending-confirmation"].includes(context.shipment.customsStatus),
      showAdditionalValidation:
        options.showAdditionalValidation ??
        (!!options.additionalValidation?.issues && options.additionalValidation.issues.length > 0),
      // Add side effects to fragment context for processor collection
      ...sideEffects
    };

    this.logger.log(
      `[PDF_ATTACHMENT_TRACKING] Final consolidatedContext has CAD document: ${!!consolidatedContext.cadDocument}, side effects: ${JSON.stringify(Object.keys(sideEffects))}`
    );
    if (consolidatedContext.cadDocument) {
      this.logger.log(
        `[PDF_ATTACHMENT_TRACKING] CAD document details in consolidatedContext: ${JSON.stringify({
          fileName: consolidatedContext.cadDocument.fileName,
          mimeType: consolidatedContext.cadDocument.mimeType,
          b64DataLength: consolidatedContext.cadDocument.b64Data?.length || 0
        })}`
      );
    }

    return [
      { template: "consolidated/main-messages", priority: 1, fragmentContext: consolidatedContext },
      { template: "consolidated/validation-issues", priority: 2, fragmentContext: consolidatedContext },
      { template: "consolidated/details-header", priority: 3, fragmentContext: consolidatedContext },
      { template: "consolidated/shipment-identifiers", priority: 4, fragmentContext: consolidatedContext },
      { template: "consolidated/document-status", priority: 5, fragmentContext: consolidatedContext },
      { template: "consolidated/status-line", priority: 6, fragmentContext: consolidatedContext }
    ];
  }

  /**
   * Build validation issues from shipment context.
   * Analyzes the context to identify missing documents and fields.
   */
  protected buildValidationIssues(
    context: ShipmentContext
  ): ConsolidatedFragmentContext["validationIssues"] | null {
    const issues: ConsolidatedFragmentContext["validationIssues"] = {
      missingDocuments: [],
      missingFields: []
    };

    // Check for missing documents based on customs status
    if (context.shipment.customsStatus === "pending-commercial-invoice") {
      if (!context.documentReceiptStatus?.ciReceived) {
        issues.missingDocuments.push("CI_PL");
      }
    }

    // Check for missing fields from context
    if (context.missingFieldsAnalysis?.formattedMissingFields?.length > 0) {
      const missingFieldsAnalysis = context.missingFieldsAnalysis;

      // Check for weight in structured measurements array
      if (missingFieldsAnalysis.missingMeasurements?.includes("weight")) {
        issues.missingFields.push("weight");
      }

      // Check for port code in structured locations array
      if (missingFieldsAnalysis.missingLocations?.includes("portCode")) {
        issues.missingFields.push("port_code");
      }

      // Check for CCN
      if (!context.shipment.cargoControlNumber) {
        issues.missingFields.push("ccn");
      }

      // Check OGD filing status
      if (context.missingFieldsAnalysis?.ogdFilingStatus === "pending") {
        issues.missingFields.push("ogd_filing");
      }
    }

    // Return null if no issues
    if (issues.missingDocuments.length === 0 && issues.missingFields.length === 0) {
      return null;
    }

    return issues;
  }

  /**
   * Build status message based on intent and customs status.
   * Generates appropriate status messages for different shipment states.
   */
  protected buildStatusMessage(context: ShipmentContext): string {
    const { shipment, smartTemplateContext } = context;

    switch (shipment.customsStatus) {
      case "pending-commercial-invoice":
        return "Please send the missing document shown below at your earliest convenience for the subject shipment, so we can file customs without delay.";

      case "pending-confirmation":
        return "There are compliance issues or missing required fields preventing submission of the subject shipment. Please respond to the email and provide additional information for the missing fields.";

      case "pending-arrival":
        return smartTemplateContext.hasETA
          ? `The estimated time of arrival (ETA) at the port for the subject shipment is ${smartTemplateContext.etaDate}. We expect to submit the customs entry as the arrival date approaches.`
          : "The submission is pending the shipment's arrival. We expect to submit the customs entry as the arrival date approaches.";

      case "live":
        return "The submission for the subject shipment has been initiated. We will let you know once released by customs.";

      case "entry-submitted":
        return "The entry for the subject shipment has been submitted. We will let you know once released by customs.";

      case "entry-accepted":
        return "The subject shipment entry has been accepted by Customs and is awaiting arrival of goods.";

      case "exam":
        return "The subject shipment has been selected by customs for examination. We are contacting you for further information.";

      case "released":
        return smartTemplateContext.formattedReleaseDate
          ? `The subject shipment has been released by CBSA on ${smartTemplateContext.formattedReleaseDate}.`
          : "The subject shipment has been released by CBSA.";

      default:
        return `Status update: ${shipment.customsStatus}`;
    }
  }

  /**
   * Build CAD document request message object for mainMessages array.
   * Uses template-based approach for human-editable content.
   * Returns message object with type 'cad' and optional attachments, or null if no user-facing message needed.
   */
  protected buildCadMessage(
    context: ShipmentContext,
    cadDocument?: any
  ): { type: string; attachments?: any } | null {
    const { shipment } = context;

    // Return null for pending-arrival (should trigger backoffice alert instead)
    if (shipment.customsStatus === "pending-arrival") {
      return null;
    }

    return {
      type: "cad",
      attachments: cadDocument ? { cadDocument } : undefined
    };
  }

  /**
   * Build rush processing request message object for mainMessages array.
   * Uses template-based approach for human-editable content.
   * For pending statuses, falls back to general status message.
   */
  protected buildRushMessage(context: ShipmentContext): { type: string } | { content: string } {
    const { shipment } = context;

    // For pending statuses, use general status message instead of rush template
    if (
      shipment.customsStatus === "pending-commercial-invoice" ||
      shipment.customsStatus === "pending-confirmation"
    ) {
      return {
        content: this.buildStatusMessage(context)
      };
    }

    // For all other statuses, use rush template
    return {
      type: "rush"
    };
  }

  /**
   * Build RNS proof request message object for mainMessages array.
   * Uses template-based approach for human-editable content.
   * Returns message object with type 'rns' and optional RNS data attachments.
   */
  protected buildRnsMessage(context: ShipmentContext, rnsData?: any): { type: string; attachments?: any } {
    return {
      type: "rns",
      attachments: rnsData ? { rnsData } : undefined
    };
  }
}
