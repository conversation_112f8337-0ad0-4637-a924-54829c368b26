# Aggregation Status in Email Response Templates

## Overview

This implementation adds **aggregation status** to email response templates, showing customers the complete processing pipeline status for their documents. Previously, emails only showed document extraction status (`extracted`), but now they also show the business processing status (`success`, `failed`, `processing`, `pending`).

## What Was Changed

### 1. **DocumentProcessorService Updates**

**File:** `apps/portal-api/src/core-agent/services/document-processor.service.ts`

#### Changes Made:
- **Enhanced `lookupAttachmentStatuses()` method** to fetch both document status and aggregation status
- **Updated `processDocumentAttachments()` method** to include aggregation status in returned objects
- **Updated `fetchProcessedDocumentsFromDatabase()` method** to include aggregation status from database

#### Database Queries:
```sql
-- Now fetches both document and aggregation status
SELECT d.id, d.status, d."aggregationId", da.status as aggregation_status 
FROM document d 
LEFT JOIN document_aggregation da ON d."aggregationId" = da.id 
WHERE d."shipmentId" = ?
```

#### Return Object Structure:
```javascript
{
  filename: "commercial-invoice.pdf",
  contentType: "Commercial Invoice", 
  status: "extracted",                    // Document extraction status
  aggregationStatus: "success",           // NEW: Aggregation processing status
  claroUrl: "https://portal.clarocustoms.com/document/510"
}
```

### 2. **Email Template Updates**

#### Files Updated:
- `apps/portal-api/src/core-agent/templates/consolidated/messages/document-processing-messages.njk`
- `apps/portal-api/src/email/templates/document-sent-in-response-email.njk`
- `apps/portal-api/src/email/templates/document-sent-in-response-with-queries-email.njk`

#### Template Changes:
```nunjucks
<li>Status: {{ document.status or 'processed' }}</li>
{%- if document.aggregationStatus %}
<li>Aggregation: <strong>{{ document.aggregationStatus | capitalize }}</strong>
  {%- if document.aggregationStatus == 'success' %} ✅
  {%- elif document.aggregationStatus == 'failed' %} ❌
  {%- elif document.aggregationStatus == 'processing' %} 🔄
  {%- elif document.aggregationStatus == 'pending' %} ⏳
  {%- endif %}
</li>
{%- endif %}
```

## Status Meanings

### Document Status (Extraction Phase)
- **`pending`** - Document uploaded, waiting to be processed
- **`extracting`** - Currently extracting data from document
- **`extracted`** - Data extracted from document (OCR/parsing complete)
- **`validating`** - Validating extracted data

### Aggregation Status (Business Processing Phase)
- **`pending`** ⏳ - Waiting for aggregation to start
- **`processing`** 🔄 - Currently aggregating data into business objects
- **`success`** ✅ - **Fully processed and ready for customs filing**
- **`failed`** ❌ - Aggregation failed, requires attention

## Email Output Example

**Before:**
```
We've received and processed the following document(s):

• Commercial Invoice
  - Status: extracted
  - Claro URL: https://portal.clarocustoms.com/document/510
```

**After:**
```
We've received and processed the following document(s):

• Commercial Invoice
  - Status: extracted
  - Aggregation: Success ✅
  - Claro URL: https://portal.clarocustoms.com/document/510
```

## Database Schema

### Key Tables:
1. **`document`** - Contains extraction status
2. **`document_aggregation`** - Contains business processing status

### Relationship:
```sql
document.aggregationId → document_aggregation.id
```

## Testing

### Test Script Created:
`apps/portal-api/src/core-agent/testing/test-aggregation-status.js`

### Test Coverage:
1. **Service method testing** - Verify aggregation status lookup
2. **Database integration** - Verify SQL queries work correctly
3. **Template rendering** - Verify status appears in email templates

### Running Tests:
```bash
cd apps/portal-api
node src/core-agent/testing/test-aggregation-status.js
```

## Customer Benefits

### 1. **Complete Transparency**
Customers now see the full document processing pipeline:
- **Extraction**: "We got your document"
- **Aggregation**: "We processed it for customs"

### 2. **Clear Status Indicators**
Visual indicators make status immediately clear:
- ✅ **Success** = Ready for customs
- 🔄 **Processing** = Still working on it
- ❌ **Failed** = Needs attention
- ⏳ **Pending** = Waiting in queue

### 3. **Matches Portal UI**
Email status now matches what customers see in the web portal, eliminating confusion.

## Technical Implementation Notes

### 1. **Performance Considerations**
- Added `LEFT JOIN` to fetch aggregation status in single query
- Minimal performance impact due to indexed foreign key relationships

### 2. **Backward Compatibility**
- Templates gracefully handle missing aggregation status
- Existing functionality unchanged for documents without aggregations

### 3. **Error Handling**
- Fallback to document status if aggregation lookup fails
- Graceful degradation for edge cases

## Future Enhancements

### 1. **Real-time Updates**
Could add WebSocket updates when aggregation status changes

### 2. **Detailed Error Messages**
Could show specific aggregation failure reasons

### 3. **Progress Indicators**
Could show aggregation step progress for complex documents

---

## Verification Checklist

- [x] Document processor service updated
- [x] Database queries include aggregation status
- [x] Email templates show aggregation status
- [x] Visual indicators added (✅❌🔄⏳)
- [x] Backward compatibility maintained
- [x] Test script created
- [x] Documentation completed

**Status:** ✅ **Implementation Complete** 