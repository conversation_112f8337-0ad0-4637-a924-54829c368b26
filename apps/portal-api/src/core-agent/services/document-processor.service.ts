import { Injectable, Logger } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { DataSource } from "typeorm";
import { DocumentStatus, FileStatus } from "nest-modules";
import { ShipmentContext } from "../../agent-context/types/shipment-context.types";
import { ValidatedIntent } from "../types/response-fragment.types";

/**
 * Service for processing documents and attachments for intent handlers.
 * Provides utilities for validating, processing, and fetching document data.
 */
@Injectable()
export class DocumentProcessorService {
  private readonly logger = new Logger(DocumentProcessorService.name);

  constructor(@InjectDataSource() private readonly dataSource: DataSource) {}

  /**
   * Validates CAD attachment structure and content.
   */
  validateCADAttachment(attachment: any): attachment is {
    fileName: string;
    mimeType: string;
    b64Data: string;
  } {
    return (
      attachment &&
      typeof attachment === "object" &&
      typeof attachment.fileName === "string" &&
      attachment.fileName.length > 0 &&
      typeof attachment.mimeType === "string" &&
      attachment.mimeType.length > 0 &&
      typeof attachment.b64Data === "string" &&
      attachment.b64Data.length > 0
    );
  }

  /**
   * Processes document attachments from validated intent and formats them for template rendering.
   */
  async processDocumentAttachments(validatedIntent: ValidatedIntent): Promise<any[]> {
    if (!validatedIntent.attachments || validatedIntent.attachments.length === 0) {
      return [];
    }

    this.logger.log(`Processing ${validatedIntent.attachments.length} document attachments`);

    // Look up actual status for attachments that have IDs
    const attachmentStatuses = await this.lookupAttachmentStatuses(
      validatedIntent.attachments.filter((att) => att.id).map((att) => att.id!)
    );

    return validatedIntent.attachments.map((attachment, index) => {
      // Provide better fallback names based on attachment properties
      let filename = attachment.filename;

      if (!filename || filename.trim() === "") {
        // Try to get filename from documentType or extractedData
        if (attachment.documentType) {
          filename = attachment.documentType.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
        } else {
          filename = `Document ${index + 1}`;
        }
      }

      this.logger.debug(
        `Processing attachment ${index + 1}: ` +
          `originalFilename="${attachment.filename}", ` +
          `documentType="${attachment.documentType}", ` +
          `finalName="${filename}", ` +
          `id="${attachment.id}"`
      );

      // Get actual status if available, otherwise use fallback
      const actualStatus = attachment.id ? attachmentStatuses.get(attachment.id) : null;
      const displayStatus = actualStatus
        ? this.formatStatusForDisplay(actualStatus.documentStatus)
        : "processed";
      const aggregationStatus = actualStatus?.aggregationStatus;

      return {
        filename: filename, // Changed from 'name' to 'filename' to match template
        contentType: this.formatDocumentTypeName(attachment.documentType || "Document"), // Format document type for display
        status: displayStatus, // Use actual status from database or fallback
        aggregationStatus: aggregationStatus, // Include aggregation status
        claroUrl: attachment.id
          ? `https://portal.clarocustoms.com/document/${attachment.id}`
          : `https://portal.clarocustoms.com/documents`
      };
    });
  }

  /**
   * Fetches processed documents from database when attachments are not available in the intent.
   * This queries the actual database for documents and files associated with the shipment.
   */
  async fetchProcessedDocumentsFromDatabase(context: ShipmentContext): Promise<any[]> {
    const processedDocuments: any[] = [];

    try {
      const shipmentId = context.shipment.id;
      const organizationId = context.organization.id;

      this.logger.debug(`📄 FETCHING DOCUMENTS: Querying database for shipment ${shipmentId} documents...`);

      // Fetch documents from Document table with aggregation status
      const documentRepository = this.dataSource.getRepository("Document");
      const documents = await documentRepository.find({
        where: {
          shipmentId,
          organizationId
        },
        relations: ["documentType", "aggregation"]
      });

      // Fetch files from File table
      const fileRepository = this.dataSource.getRepository("File");
      const files = await fileRepository.find({
        where: {
          shipmentId,
          organizationId
        }
      });

      this.logger.debug(
        `📄 FETCHED FROM DB: ${documents.length} documents, ${files.length} files for shipment ${shipmentId}`
      );

      // Process documents
      documents.forEach((doc, index) => {
        const documentName =
          doc.name && doc.name.trim() !== "" ? doc.name : doc.documentType?.name || `Document ${index + 1}`;

        processedDocuments.push({
          filename: documentName, // Changed from 'name' to 'filename' to match template
          contentType: this.formatDocumentTypeName(doc.name || doc.documentType?.name), // Use Document.name field for document type
          status: this.formatStatusForDisplay(doc.displayStatus || doc.status), // Use displayStatus (considers shipment mismatch) or fallback to status
          aggregationStatus: doc.aggregation?.status, // Include aggregation status
          claroUrl: `https://portal.clarocustoms.com/shipment/${shipmentId}/document/${doc.id}`
        });
      });

      // Process files
      files.forEach((file, index) => {
        const fileName = file.name && file.name.trim() !== "" ? file.name : `File ${index + 1}`;

        processedDocuments.push({
          filename: fileName, // Changed from 'name' to 'filename' to match template
          contentType: "File", // Files don't have document types from aggregation
          status: this.formatStatusForDisplay(file.status), // Use actual file status
          aggregationStatus: undefined, // Files don't have aggregation status
          claroUrl: `https://portal.clarocustoms.com/shipment/${shipmentId}/file/${file.id}`
        });
      });

      // No fallback to commercial invoices - if no actual documents/files were processed, return empty array
      // This is more honest - commercial invoices are business data, not "processed documents"
    } catch (error) {
      this.logger.error(
        `Failed to fetch documents from database for shipment ${context.shipment.id}: ${error.message}`,
        error.stack
      );
    }

    return processedDocuments;
  }

  /**
   * Formats document type name for display (converts enum values to readable names).
   * Example: "COMMERCIAL_INVOICE" -> "Commercial Invoice"
   */
  formatDocumentTypeName(documentType: string): string {
    // Handle undefined, null, or empty string cases
    if (!documentType || documentType.trim() === "") {
      return "Document";
    }

    return documentType
      .replace(/_/g, " ")
      .toLowerCase()
      .replace(/\b\w/g, (char) => char.toUpperCase());
  }

  /**
   * Looks up actual status values for attachments by their IDs.
   * Attachments could be either Documents or Files, so we check both tables.
   * Also looks up aggregation status for documents.
   */
  private async lookupAttachmentStatuses(
    attachmentIds: number[]
  ): Promise<Map<number, { documentStatus: DocumentStatus | FileStatus; aggregationStatus?: string }>> {
    const statusMap = new Map<
      number,
      { documentStatus: DocumentStatus | FileStatus; aggregationStatus?: string }
    >();

    if (attachmentIds.length === 0) {
      return statusMap;
    }

    try {
      // Look up in Document table first with aggregation status
      const documentRepository = this.dataSource.getRepository("Document");
      const documents = await documentRepository.find({
        where: { id: attachmentIds as any },
        select: ["id", "status", "displayStatus", "aggregationId"],
        relations: ["aggregation"]
      });

      // Get aggregation statuses for documents that have aggregations
      const aggregationIds = documents.filter((doc) => doc.aggregationId).map((doc) => doc.aggregationId);

      const aggregationStatuses = new Map<number, string>();
      if (aggregationIds.length > 0) {
        const aggregationRepository = this.dataSource.getRepository("DocumentAggregation");
        const aggregations = await aggregationRepository.find({
          where: { id: aggregationIds as any },
          select: ["id", "status"]
        });

        aggregations.forEach((agg) => {
          aggregationStatuses.set(agg.id, agg.status);
        });
      }

      documents.forEach((doc) => {
        // Use displayStatus if available (considers shipment mismatch), otherwise use status
        const documentStatus = doc.displayStatus || doc.status;
        const aggregationStatus = doc.aggregationId ? aggregationStatuses.get(doc.aggregationId) : undefined;

        statusMap.set(doc.id, {
          documentStatus,
          aggregationStatus
        });
      });

      // Look up remaining IDs in File table
      const remainingIds = attachmentIds.filter((id) => !statusMap.has(id));
      if (remainingIds.length > 0) {
        const fileRepository = this.dataSource.getRepository("File");
        const files = await fileRepository.find({
          where: { id: remainingIds as any },
          select: ["id", "status"]
        });

        files.forEach((file) => {
          statusMap.set(file.id, {
            documentStatus: file.status,
            aggregationStatus: undefined
          });
        });
      }

      this.logger.debug(`Looked up status for ${statusMap.size}/${attachmentIds.length} attachments`);
    } catch (error) {
      this.logger.error(`Failed to lookup attachment statuses: ${error.message}`);
    }

    return statusMap;
  }

  /**
   * Formats status values for display (converts enum values to lowercase, no underscores).
   * Example: "EXTRACTION_FAILED" -> "extraction failed"
   */
  private formatStatusForDisplay(status: DocumentStatus | FileStatus): string {
    return status.replace(/_/g, " ").toLowerCase();
  }
}
