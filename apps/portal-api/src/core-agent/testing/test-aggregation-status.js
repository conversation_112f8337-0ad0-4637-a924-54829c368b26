#!/usr/bin/env node

/**
 * Test script to verify aggregation status lookup functionality
 *
 * This script tests:
 * 1. DocumentProcessorService.processDocumentAttachments() with aggregation status
 * 2. DocumentProcessorService.fetchProcessedDocumentsFromDatabase() with aggregation status
 * 3. SQL queries to verify database structure
 */

const { NestFactory } = require("@nestjs/core");
const { AppModule } = require("../../dist/app.module");
const { DocumentProcessorService } = require("../../dist/core-agent/services/document-processor.service");

async function testAggregationStatusLookup() {
  console.log("🧪 Testing Aggregation Status Lookup");
  console.log("=====================================");

  try {
    // Create NestJS application
    const app = await NestFactory.createApplicationContext(AppModule);
    const documentProcessorService = app.get(DocumentProcessorService);

    console.log("✅ NestJS application context created");

    // Test 1: processDocumentAttachments with mock data
    console.log("\n📋 Test 1: processDocumentAttachments with aggregation status");
    console.log("-----------------------------------------------------------");

    const mockValidatedIntent = {
      attachments: [
        {
          id: 510, // Document ID from our earlier test
          filename: "commercial-invoice.pdf",
          documentType: "COMMERCIAL_INVOICE"
        },
        {
          id: 511, // Document ID from our earlier test
          filename: "house-bill-of-lading.pdf",
          documentType: "HOUSE_OCEAN_BILL_OF_LADING"
        }
      ]
    };

    const processedDocuments = await documentProcessorService.processDocumentAttachments(mockValidatedIntent);

    console.log(`Found ${processedDocuments.length} processed documents:`);
    processedDocuments.forEach((doc, index) => {
      console.log(`  Document ${index + 1}:`);
      console.log(`    filename: "${doc.filename}"`);
      console.log(`    contentType: "${doc.contentType}"`);
      console.log(`    status: "${doc.status}"`);
      console.log(`    aggregationStatus: "${doc.aggregationStatus || "N/A"}"`);
      console.log(`    claroUrl: "${doc.claroUrl}"`);

      if (doc.aggregationStatus) {
        console.log(`    ✅ Aggregation status found: ${doc.aggregationStatus}`);
      } else {
        console.log(`    ⚠️  No aggregation status found`);
      }
    });

    // Test 2: fetchProcessedDocumentsFromDatabase with mock context
    console.log("\n📋 Test 2: fetchProcessedDocumentsFromDatabase with aggregation status");
    console.log("--------------------------------------------------------------------");

    const mockContext = {
      shipment: { id: 887 }, // Shipment ID from our earlier test
      organization: { id: 3 } // Organization ID from our earlier test
    };

    const fetchedDocuments = await documentProcessorService.fetchProcessedDocumentsFromDatabase(mockContext);

    console.log(`Found ${fetchedDocuments.length} fetched documents:`);
    fetchedDocuments.forEach((doc, index) => {
      console.log(`  Document ${index + 1}:`);
      console.log(`    filename: "${doc.filename}"`);
      console.log(`    contentType: "${doc.contentType}"`);
      console.log(`    status: "${doc.status}"`);
      console.log(`    aggregationStatus: "${doc.aggregationStatus || "N/A"}"`);
      console.log(`    claroUrl: "${doc.claroUrl}"`);

      if (doc.aggregationStatus) {
        console.log(`    ✅ Aggregation status found: ${doc.aggregationStatus}`);
      } else {
        console.log(`    ⚠️  No aggregation status found`);
      }
    });

    // Test 3: Direct SQL verification
    console.log("\n📋 Test 3: Direct SQL verification");
    console.log("----------------------------------");

    const dataSource = app.get("DataSource");
    const queryResult = await dataSource.query(
      `
      SELECT 
        d.id, 
        d.name, 
        d.status, 
        d."aggregationId", 
        da.status as aggregation_status 
      FROM document d 
      LEFT JOIN document_aggregation da ON d."aggregationId" = da.id 
      WHERE d."shipmentId" = $1 
      ORDER BY d.id
    `,
      [887]
    );

    console.log(`SQL Query Results (${queryResult.length} rows):`);
    queryResult.forEach((row, index) => {
      console.log(`  Row ${index + 1}:`);
      console.log(`    id: ${row.id}`);
      console.log(`    name: ${row.name}`);
      console.log(`    status: ${row.status}`);
      console.log(`    aggregationId: ${row.aggregationId || "N/A"}`);
      console.log(`    aggregation_status: ${row.aggregation_status || "N/A"}`);

      if (row.aggregation_status) {
        console.log(`    ✅ Aggregation status: ${row.aggregation_status}`);
      } else {
        console.log(`    ⚠️  No aggregation status`);
      }
    });

    console.log("\n🎉 All tests completed successfully!");

    await app.close();
  } catch (error) {
    console.error("❌ Test failed:", error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the test
testAggregationStatusLookup().catch(console.error);
