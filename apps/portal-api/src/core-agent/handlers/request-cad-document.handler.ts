import { Injectable, Inject } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ResponseFragment, ValidatedIntent, FragmentContext } from "../types/response-fragment.types";
import { ShipmentContext } from "../../agent-context";
import { CommercialInvoice, Shipment } from "nest-modules";
import { RNSStatusChangeEmailSender } from "../../shipment/senders/rns-status-change-email.sender";
import { ImporterService } from "../../importer/importer.service";
import { DataSource } from "typeorm";
import { ShipmentResponseService } from "../services/shipment-response.service";

/**
 * <PERSON>les requests for CAD (Canada Entry) documents.
 * Evaluates business rules and generates CAD documents when possible.
 */
@Injectable()
export class RequestCADDocumentHandler extends BaseIntentHandler {
  constructor(
    private readonly rnsStatusChangeEmailSender: RNSStatusChangeEmailSender,
    private readonly importerService: ImporterService,
    private readonly dataSource: DataSource,
    @Inject(ShipmentResponseService) private readonly shipmentResponseService: ShipmentResponseService
  ) {
    super();
  }

  readonly classificationMeta: IntentClassificationMeta = {
    intent: "REQUEST_CAD_DOCUMENT" as const,
    description:
      "User is requesting for the Customs Accounting Document, or CAD, for this shipment to be generated and sent to them.",
    examples: [
      "Can you send me the CAD document?",
      "I need the Canada Entry",
      "Please provide the customs entry document",
      "Send me the entry paperwork"
    ],
    keywords: ["cad", "canada entry", "customs entry", "entry document", "paperwork"]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
    this.logger.log(`[REQUEST_CAD_DOCUMENT] Handling request for shipment ${context.shipment?.id || "N/A"}`);

    if (!context.shipment) {
      this.logger.error("Cannot generate CAD document: no shipment found in context");
      throw new Error("No shipment found for CAD document generation");
    }

    try {
      // Step 1: Build main messages array for consolidated templates
      const mainMessages = await this.buildMainMessages(context);
      this.logger.debug(`[REQUEST_CAD_DOCUMENT] Built ${mainMessages.length} main messages`);

      // Step 2: Determine conditional fragment flags
      const consolidatedOptions = this.buildConsolidatedOptions(context);
      this.logger.debug(
        `[REQUEST_CAD_DOCUMENT] Consolidated options: ${JSON.stringify(consolidatedOptions)}`
      );

      // Step 3: Use consolidated fragment system
      const fragments = this.createConsolidatedFragments(mainMessages, context, consolidatedOptions);

      this.logger.log(
        `[REQUEST_CAD_DOCUMENT] Generated ${fragments.length} consolidated fragments for shipment ${context.shipment.id}`
      );

      return fragments;
    } catch (error) {
      this.logger.error(
        `Failed to generate CAD document for shipment ${context.shipment.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Build main messages array for consolidated templates
   * MIGRATION: Replaces CAD response fragment logic
   */
  private async buildMainMessages(
    context: ShipmentContext
  ): Promise<Array<{ content?: string; type?: string; priority: number; attachments?: any }>> {
    const mainMessages: Array<{ content?: string; type?: string; priority: number; attachments?: any }> = [];

    // Generate CAD document if available
    let cadDocument = undefined;
    if (context.smartTemplateContext.cadDocumentAvailable) {
      try {
        const cadData = await this.generateCADDocument(context.shipment);
        cadDocument = cadData.cadDocument;
        this.logger.log(
          `[PDF_ATTACHMENT_TRACKING] Generated CAD document for shipment ${context.shipment.id}, cadDocument present: ${!!cadDocument}, fileName: ${cadDocument?.fileName}, b64DataLength: ${cadDocument?.b64Data?.length || 0}`
        );
      } catch (error) {
        this.logger.error(`[REQUEST_CAD_DOCUMENT] Failed to generate CAD document: ${error.message}`);
        // Continue without CAD document - template will handle the error state
      }
    } else {
      this.logger.log(
        `[PDF_ATTACHMENT_TRACKING] CAD document not available for shipment ${context.shipment.id} - cadDocumentAvailable: ${context.smartTemplateContext.cadDocumentAvailable}`
      );
    }

    // Build CAD message using template-based approach
    const cadMessage = this.buildCadMessage(context, cadDocument);
    if (cadMessage) {
      mainMessages.push({
        type: cadMessage.type,
        priority: 1,
        attachments: cadMessage.attachments
      });
    }

    return mainMessages;
  }

  /**
   * Build consolidated options for conditional fragment control
   * MIGRATION: Replaces individual fragment addition logic
   */
  private buildConsolidatedOptions(context: ShipmentContext): {
    showValidationIssues?: boolean;
    showDocumentStatus?: boolean;
    showAdditionalValidation?: boolean;
    validationIssues?: any;
    additionalValidation?: { issues: string[] };
  } {
    // For CAD requests, show validation issues if the shipment has pending statuses
    const shouldShowValidationIssues = ["pending-commercial-invoice", "pending-confirmation"].includes(
      context.shipment.customsStatus
    );

    // Build validation issues if needed
    let validationIssues = null;
    if (shouldShowValidationIssues) {
      validationIssues = this.buildValidationIssues(context);
    }

    const options = {
      showValidationIssues: shouldShowValidationIssues && !!validationIssues,
      showDocumentStatus: shouldShowValidationIssues, // Show document status for pending statuses
      showAdditionalValidation: false, // Not used for CAD requests
      validationIssues,
      additionalValidation: undefined
    };

    this.logger.debug(
      `[REQUEST_CAD_DOCUMENT] Consolidated options - showValidationIssues: ${options.showValidationIssues}, showDocumentStatus: ${options.showDocumentStatus}`
    );

    return options;
  }

  /**
   * Generate CAD document and return as fragment context data
   */
  private async generateCADDocument(shipment: Shipment): Promise<Partial<FragmentContext>> {
    // Get commercial invoices for the shipment
    const commercialInvoices = await this.getCommercialInvoices(shipment.id);

    if (commercialInvoices.length === 0) {
      throw new Error("No commercial invoices found for this shipment");
    }

    // Get organization importer
    const importersResponse = await this.importerService.getImporters({
      organizationId: shipment.organizationId,
      limit: 1
    });
    const organizationImporter =
      importersResponse.importers.length > 0 ? importersResponse.importers[0] : null;

    // Generate CAD attachment
    const cadAttachment = await this.rnsStatusChangeEmailSender.createCADAttachment(
      shipment,
      commercialInvoices,
      organizationImporter
    );

    // Clean the base64 string
    const cleanedCadAttachment = {
      ...cadAttachment,
      b64Data: cadAttachment.b64Data.replace(/[\s\r\n\t]/g, "")
    };

    this.logger.log(`Successfully generated CAD document for shipment ${shipment.id}.`);

    return {
      cadDocument: cleanedCadAttachment
    };
  }

  /**
   * Get commercial invoices for the shipment using TypeORM
   */
  private async getCommercialInvoices(shipmentId: number): Promise<CommercialInvoice[]> {
    try {
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();

      try {
        const commercialInvoices = await queryRunner.manager.getRepository(CommercialInvoice).find({
          where: { shipment: { id: shipmentId } },
          relations: {
            vendor: true,
            purchaser: true,
            countryOfExport: true,
            stateOfExport: true,
            commercialInvoiceLines: {
              origin: true,
              originState: true,
              tt: true,
              tariffCode: true
            }
          }
        });

        this.logger.log(`Found ${commercialInvoices.length} commercial invoices for shipment ${shipmentId}`);
        return commercialInvoices;
      } finally {
        await queryRunner.release();
      }
    } catch (error) {
      this.logger.error(
        `Failed to get commercial invoices for shipment ${shipmentId}: ${error.message}`,
        error.stack
      );
      return [];
    }
  }
}
