{# Document Processing Messages Template
   
   Data dependencies:
   - message.attachments.documents: Array of processed documents
   - message.attachments.hasAllRequiredDocuments: Whether all documents are complete
   - message.attachments.acknowledgment: Custom acknowledgment message
   - shipment.customsStatus: Current customs status
   - smartTemplateContext.transportMode: Transport mode
   
   Returns document processing acknowledgment message based on documents and status
   NOTE: CAD document handling is separate - handled by cad-messages.njk when CAD is attached
   
   IMPORTANT: This template mirrors the original enhanced-document-processing-acknowledgment.njk
#}

{# --- Document List Section --- #}
{%- if message.attachments.documents and (message.attachments.documents | length) > 0 %}
<p style="margin-top: 15px; margin-bottom: 5px;">We've received and processed the following document(s):</p>
<ul style="list-style-type: none; padding-left: 0; margin-top: 5px;">
  {%- for document in message.attachments.documents %}
  <li style="border: 1px solid #eee; padding: 10px; margin-bottom: 10px; border-radius: 4px; background-color: #fdfdfd;">
      <strong>{{ document.filename or 'Document' }}</strong>
      <ul style="margin-top: 5px; padding-left: 20px; font-size: 0.9em;">
          <li>Document Type: {{ document.contentType or 'Document' }}</li>
          <li>Status: 
            {%- if document.aggregationStatus %}
              <strong>{{ document.aggregationStatus | capitalize }}</strong>
              {%- if document.aggregationStatus == 'success' %} ✅
              {%- elif document.aggregationStatus == 'failed' %} ❌
              {%- elif document.aggregationStatus == 'processing' %} 🔄
              {%- elif document.aggregationStatus == 'pending' %} ⏳
              {%- endif %}
            {%- else %}
              {{ document.status or 'processed' }}
            {%- endif %}
          </li>
          <li>Claro URL: <a href="{{ document.claroUrl }}" style="color: #0056b3;">{{ document.claroUrl }}</a></li>
      </ul>
  </li>
  {%- endfor %}
</ul>
{%- else %}
{# Fallback when no specific documents are available #}
<p style="margin-top: 15px;">
  We have received the required documents for your shipment and are currently reviewing and processing them.
</p>
{%- endif %}
{# --- End Document List --- #}

{# Conditional Messaging Based on Document Completeness and Current Status #}
<p style="margin-top: 20px;">
{% if message.attachments.acknowledgment %}
{{ message.attachments.acknowledgment }}
{% elif message.attachments.hasAllRequiredDocuments and shipment.customsStatus != 'pending-commercial-invoice' and shipment.customsStatus != 'pending-confirmation' %}
We have received all required documents for your shipment and are currently reviewing and processing them.{% if smartTemplateContext.transportMode == 'TRUCK' %} Transaction # will be sent to you shortly.{% endif %}
{% elif shipment.customsStatus == 'pending-commercial-invoice' %}
We have received your documents. However, we're still missing the Commercial Invoice & Packing List. Please send the missing documents at your earliest convenience for the subject shipment, so we can file customs without delay.
{% elif shipment.customsStatus == 'pending-confirmation' %}
We have received your documents. However, there are compliance issues or missing required fields preventing submission of the subject shipment. Please respond to the email and provide additional information for the missing fields.
{% elif shipment.customsStatus == 'pending-arrival' %}
We have received your documents and the shipment entry is pending arrival at the destination port.
{% elif shipment.customsStatus == 'live' %}
We have received your documents and the submission for the subject shipment has been initiated. We will let you know once released by customs.
{% elif shipment.customsStatus == 'entry-submitted' %}
We have received your documents and the entry for the subject shipment has been submitted to customs. We will let you know once released by customs.
{% elif shipment.customsStatus == 'entry-accepted' %}
We have received your documents and the subject shipment entry has been accepted by Customs and is awaiting arrival of goods.
{% elif shipment.customsStatus == 'exam' %}
We have received your documents and the subject shipment has been selected by customs for examination. We are in contact for further information.
{% elif shipment.customsStatus == 'released' %}
We have received your documents and the subject shipment has been released by CBSA.
{% else %}
We have received and processed the documents for your shipment. Additional processing is underway.
{% endif %}
</p>